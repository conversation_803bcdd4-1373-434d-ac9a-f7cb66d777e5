/* Start map.css */

/** 
 * Map component styles for service area display
 * Applies to Leaflet map and custom overlays
 */

/* Map container */
#service-area-map,
.leaflet-map-container {
  width: 100%;
  height: 350px;
  min-height: 250px;
  max-width: 600px;
  margin: 0 auto;
  border-radius: 18px;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.13), 0 1.5px 6px 0 rgba(0,0,0,0.09);
  overflow: hidden;
  background: #f8fafc;
  position: relative;
  z-index: 1;
}

/* Responsive adjustments */
@media (max-width: 700px) {
  #service-area-map,
  .leaflet-map-container {
    height: 220px;
    max-width: 98vw;
    border-radius: 12px;
  }
}

/* Hide Leaflet attribution */
.leaflet-control-attribution {
  display: none !important;
}

/* Polygon overlay styling (service area) */
.leaflet-interactive {
  stroke: #2e7d32 !important; /* deep green */
  stroke-width: 3 !important;
  fill: #a5d6a7 !important;   /* light green */
  fill-opacity: 0.32 !important;
  filter: drop-shadow(0 2px 6px rgba(46,125,50,0.13));
  transition: fill-opacity 0.2s;
}

.leaflet-interactive:hover {
  fill-opacity: 0.32 !important;
  cursor: pointer;
}

/* Custom marker styling (if used) */
.map-custom-marker {
  background: #fff;
  border: 2px solid #2e7d32;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  box-shadow: 0 1px 4px rgba(46,125,50,0.13);
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-custom-marker-inner {
  width: 10px;
  height: 10px;
  background: #2e7d32;
  border-radius: 50%;
}

/* Optional: fade-in animation for map */
@keyframes mapFadeIn {
  from { opacity: 0; transform: scale(0.98);}
  to   { opacity: 1; transform: scale(1);}
}

#service-area-map,
.leaflet-map-container {
  animation: mapFadeIn 0.7s cubic-bezier(.4,1.4,.6,1) 0.1s both;
}

/* Ensure map tiles are crisp on retina/high-DPI */
.leaflet-tile {
  image-rendering: auto;
  image-rendering: crisp-edges;
  image-rendering: pixelated;
}

/* Optional: subtle border for map */
#service-area-map,
.leaflet-map-container {
  border: 3.5px solid #0072e6;
}

/* Hide scrollbars inside map */
#service-area-map::-webkit-scrollbar,
.leaflet-map-container::-webkit-scrollbar {
  display: none;
}

/* Prevent accidental text selection on map */
#service-area-map,
.leaflet-map-container,
#service-area-map * {
  user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
}

/* Custom zoom controls */
.leaflet-control-zoom {
  display: flex !important;
  flex-direction: column;
  position: absolute !important;
  top: 16px;
  right: 16px;
  z-index: 1000;
  background: rgba(255,255,255,0.95);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10);
  overflow: hidden;
}

.leaflet-control-zoom-in,
.leaflet-control-zoom-out {
  font-size: 1.5rem;
  color: #0072e6;
  background: none;
  border: none;
  outline: none;
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  cursor: pointer;
  transition: background 0.15s;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
}

.leaflet-control-zoom-in:hover,
.leaflet-control-zoom-out:hover {
  background: #e3f2fd;
}

.leaflet-control-zoom-in:active,
.leaflet-control-zoom-out:active {
  background: #bbdefb;
}

/* Prevent zoom controls from being hidden by other rules */
.leaflet-top,
.leaflet-right {
  z-index: 1100 !important;
}

/* End map.css */
